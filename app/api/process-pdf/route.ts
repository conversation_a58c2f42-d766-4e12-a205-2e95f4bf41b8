import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/server'
import { processEbook } from '@/lib/ebook-processor'

export async function POST(request: NextRequest) {
  try {
    const { projectId, pdfUrl } = await request.json()

    if (!projectId || !pdfUrl) {
      return NextResponse.json(
        { error: 'Project ID and PDF URL are required' },
        { status: 400 }
      )
    }

    const supabase = await createSupabaseServerClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Verify the project belongs to the user
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, title, user_id, ebook_file_type')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    }

    try {
      // Process the ebook file
      const result = await processEbook(pdfUrl, project.ebook_file_type || 'pdf')

      // Update project with extracted metadata
      const { error: updateError } = await supabase
        .from('projects')
        .update({
          total_chapters: result.chapters.length,
          total_words: result.wordCount,
          reading_time_minutes: result.readingTimeMinutes,
          is_complete: true,
          // Store additional metadata
          tags: result.suggestedTags || [],
          meta_description: result.description || project.meta_description
        })
        .eq('id', projectId)

      if (updateError) {
        console.error('Error updating project stats:', updateError)
        throw new Error('Failed to update project')
      }

      // Create chapters if they were extracted
      if (result.chapters.length > 0) {
        const chaptersToInsert = result.chapters.map((chapter, index) => ({
          project_id: projectId,
          user_id: user.id,
          title: chapter.title,
          content: chapter.content,
          chapter_number: index + 1,
          word_count: chapter.wordCount,
          is_published: true
        }))

        const { error: chaptersError } = await supabase
          .from('chapters')
          .insert(chaptersToInsert)

        if (chaptersError) {
          console.error('Error creating chapters:', chaptersError)
          // Don't fail the whole process, just log the error
        }
      }

      return NextResponse.json({
        success: true,
        message: 'Ebook processed successfully',
        chaptersCreated: result.chapters.length,
        totalWords: result.wordCount,
        readingTime: result.readingTimeMinutes,
        pageCount: result.pageCount,
        suggestedTags: result.suggestedTags,
        readabilityScore: result.readabilityScore
      })

    } catch (processingError) {
      console.error('Ebook processing error:', processingError)
      return NextResponse.json(
        { error: `Failed to process ebook: ${processingError.message}` },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
